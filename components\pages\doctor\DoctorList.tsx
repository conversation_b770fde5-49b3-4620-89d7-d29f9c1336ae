'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import DataTable from 'react-data-table-component';
import { <PERSON> } from './DoctorModule';

const initialDoctors: Doctor[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', username: 'johndo<PERSON>', specialty: 'Cardiology', contact: '123-456-7890' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jane<PERSON>', specialty: 'Dermatology', contact: '123-456-7891' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: '<PERSON><PERSON><PERSON><PERSON>', specialty: 'Pediatrics', contact: '123-456-7892' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ed<PERSON><PERSON>', specialty: 'Orthopedics', contact: '123-456-7893' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'wbrown', specialty: 'Neurology', contact: '123-456-7894' },
  { id: 6, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'owilson', specialty: 'Psychiatry', contact: '123-456-7895' },
];

const DoctorList: React.FC = () => {
  const [search, setSearch] = useState('');
  const [doctors, setDoctors] = useState<Doctor[]>(initialDoctors);
  const router = useRouter();

  const handleDelete = (id: number) => {
    const confirmDelete = window.confirm("Are you sure you want to delete this doctor?");
    if (confirmDelete) {
      setDoctors(prev => prev.filter(doctor => doctor.id !== id));
    }
  };

  const handleEdit = (id: number) => {
    router.push(`/doctor/${id}/edit`);
  };

  const handleView = (id: number) => {
    router.push(`/doctor/${id}/view`);
  };

  const filteredDoctors = doctors.filter(doctor =>
    `${doctor.first_name} ${doctor.last_name} ${doctor.email} ${doctor.username}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );

  const columns = [
    { name: 'First Name', selector: (row: Doctor) => row.first_name, sortable: true },
    { name: 'Last Name', selector: (row: Doctor) => row.last_name, sortable: true },
    { name: 'Email', selector: (row: Doctor) => row.email, sortable: true },
    { name: 'Username', selector: (row: Doctor) => row.username, sortable: true },
    { name: 'Specialty', selector: (row: Doctor) => row.specialty || 'Not specified', sortable: true },
    {
      name: 'Actions',
      cell: (row: Doctor) => (
        <div className="flex gap-3 items-center">
          <button type="button" onClick={() => handleView(row.id)} title="View">
            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
          </button>
          <button type="button" onClick={() => handleEdit(row.id)} title="Edit">
            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
          </button>
          <button type="button" onClick={() => handleDelete(row.id)} title="Delete">
            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Doctors List</h2>
        <button
          type="button"
          onClick={() => router.push('/doctor/new')}
          className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
        >
          Add Doctor
        </button>
      </div>

      <input
        type="text"
        placeholder="Search by name, email, or username..."
        value={search}
        onChange={e => setSearch(e.target.value)}
        className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
      />

      <DataTable
        columns={columns}
        data={filteredDoctors}
        pagination
        highlightOnHover
        responsive
        striped
        noHeader
      />
    </div>
  );
};

export default DoctorList;
