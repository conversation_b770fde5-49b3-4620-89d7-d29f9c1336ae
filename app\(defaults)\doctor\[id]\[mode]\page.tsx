'use client';

import <PERSON><PERSON><PERSON> from '@/components/pages/doctor/DoctorList';
import DoctorCRUDForm from '@/components/pages/doctor/DoctorModule';
import <PERSON><PERSON><PERSON><PERSON> from '@/components/pages/doctor/DoctorView';
import React from 'react';

export interface DoctorModuleProps {
  mode?: 'list' | 'add' | 'edit' | 'view';
  id?: string;
}

const DoctorModule: React.FC<DoctorModuleProps> = ({ mode = 'list', id }) => {
  switch (mode) {
    case 'add':
      return <DoctorCRUDForm mode="add" />;

    case 'edit':
      if (!id) return <p>No doctor ID provided for edit.</p>;
      return <DoctorCRUDForm mode="edit" id={id} />;

    case 'view':
      if (!id) return <p>No doctor ID provided for view.</p>;
      return <DoctorView id={id} />;

    case 'list':
    default:
      return <DoctorList />;
  }
};

export default DoctorModule;
